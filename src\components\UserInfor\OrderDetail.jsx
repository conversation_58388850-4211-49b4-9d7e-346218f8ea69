import React from "react";
import { Link } from "react-router";
// <PERSON><PERSON> liệu mẫu cho sản phẩm trong đơn hàng
const sampleOrder = {
  id: "OD001",
  date: "2025-07-20",
  status: "<PERSON><PERSON> xử lý",
  total: 1200000,
  address: "2972 Westheimer Rd. Santa Ana, Illinois 85486",
  items: [
    {
      name: "<PERSON>o thun nam basic",
      quantity: 2,
      price: 200000,
      image: "https://images.unsplash.com/photo-1542281286-9e0a16bb7366",
    },
    {
      name: "Quần jeans nữ",
      quantity: 1,
      price: 800000,
      image: "https://images.unsplash.com/photo-1542281286-9e0a16bb7366",
    },
  ],
};

export default function OrderDetail({ order = sampleOrder, onBack }) {
  return (
    <div className="col-span-4 max-w-6xl mx-auto bg-white rounded-xl shadow-md p-6 mt-6">
      <Link
        className="mb-4 text-blue-500 hover:underline"
        to={"/user/order"}
        type="button"
      >
        ← Quay lại danh sách đơn hàng
      </Link>
      <h2 className="text-2xl font-bold mb-6">Chi tiết đơn hàng</h2>
      <div className="flex flex-col md:flex-row gap-8">
        {/* Bên trái: Danh sách sản phẩm */}
        <div className="flex-[2] min-w-[420px]">
          <h3 className="text-xl font-semibold mb-2">Sản phẩm</h3>
          <div className="w-full">
            <div className="flex font-semibold border-b pb-2 mb-2">
              <div className="w-20">Ảnh</div>
              <div className="flex-1">Tên sản phẩm</div>
              <div className="w-24 text-center">Số lượng</div>
              <div className="w-32 text-right">Thành tiền</div>
            </div>
            {order.items.map((item, idx) => (
              <div
                key={idx}
                className="flex items-center gap-2 py-2 border-b last:border-b-0"
              >
                <div className="w-20 flex-shrink-0">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-16 h-16 object-cover rounded"
                  />
                </div>
                <div className="flex-1 font-medium">{item.name}</div>
                <div className="w-24 text-center">{item.quantity}</div>
                <div className="w-32 text-right text-green-700 font-semibold">
                  {(item.price * item.quantity).toLocaleString()}₫
                </div>
              </div>
            ))}
          </div>
        </div>
        {/* Bên phải: Thông tin đơn hàng */}
        <div className="flex-1 md:max-w-xs">
          <div className="bg-gray-50 rounded-lg p-4 shadow-sm">
            <div className="mb-2">
              <span className="font-semibold">Mã đơn:</span> {order.id}
            </div>
            <div className="mb-2">
              <span className="font-semibold">Ngày đặt:</span> {order.date}
            </div>
            <div className="mb-2">
              <span className="font-semibold">Trạng thái:</span>{" "}
              <span
                className={
                  order.status === "Đã giao"
                    ? "bg-green-500 text-white px-2 py-1 rounded-2xl"
                    : order.status === "Đã hủy"
                    ? "bg-red-500 text-white px-2 py-1 rounded-2xl"
                    : "bg-yellow-600 text-white px-2 py-1 rounded-2xl"
                }
              >
                {order.status}
              </span>
            </div>
            <div className="mb-2">
              <span className="font-semibold">Địa chỉ nhận hàng:</span>{" "}
              {order.address}
            </div>
            <div className="mb-2">
              <span className="font-semibold">Tổng tiền:</span>{" "}
              {order.total.toLocaleString()}₫
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
