import { useState, useEffect } from "react";

const Carousel = ({ slides, autoPlay = true, interval = 3000 }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const goToPrevious = () => {
    const isFirstSlide = currentIndex === 0;
    const newIndex = isFirstSlide ? slides.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const goToNext = () => {
    const isLastSlide = currentIndex === slides.length - 1;
    const newIndex = isLastSlide ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  };

  const goToSlide = (slideIndex) => {
    setCurrentIndex(slideIndex);
  };

  useEffect(() => {
    if (!autoPlay) return;

    const timer = setTimeout(() => {
      goToNext();
    }, interval);

    return () => clearTimeout(timer);
  }, [currentIndex, autoPlay, interval]);

  return (
    <div className="h-96 w-full ">
      <div className="group relative h-full w-full">
        {/* Left Arrow - Shows on hover */}
        <button
          onClick={goToPrevious}
          className="absolute left-4 top-1/2 z-10 -translate-y-1/2 transform rounded-full bg-black/30 p-2 text-white opacity-0 transition-opacity duration-300 group-hover:opacity-100"
        >
          ❮
        </button>

        {/* Slides */}
        <div className="h-full w-full overflow-hidden rounded-lg">
          <div
            className="h-full w-full bg-cover bg-center transition-all duration-500 ease-in-out"
            style={{ backgroundImage: `url(${slides[currentIndex].url})` }}
          >
            <div className="flex h-full items-end pb-12">
              <div className="w-full bg-black/40 p-4 text-white">
                <h2 className="text-2xl font-bold">
                  {slides[currentIndex].title}
                </h2>
                <p>{slides[currentIndex].description}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Right Arrow - Shows on hover */}
        <button
          onClick={goToNext}
          className="absolute right-4 top-1/2 z-10 -translate-y-1/2 transform rounded-full bg-black/30 p-2 text-white opacity-0 transition-opacity duration-300 group-hover:opacity-100"
        >
          ❯
        </button>

        {/* Dots Indicator */}
        <div className="absolute bottom-4 left-1/2 flex -translate-x-1/2 transform space-x-2">
          {slides.map((slide, slideIndex) => (
            <button
              key={slideIndex}
              onClick={() => goToSlide(slideIndex)}
              className={`h-3 w-3 rounded-full transition-all ${
                slideIndex === currentIndex ? "bg-white" : "bg-white/50"
              }`}
            ></button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Carousel;
