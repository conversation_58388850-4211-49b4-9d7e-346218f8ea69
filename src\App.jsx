import React from "react";
import { <PERSON>rowser<PERSON>outer, Route, Routes } from "react-router";
import Home from "./pages/Home";
import Layout from "./layouts/Layout";
import Login from "./pages/Login";
import Register from "./pages/Register";
import UserInfor from "./pages/UserInfor";
import MyAddress from "./components/UserInfor/MyAddress";
import AccountDetails from "./components/UserInfor/AccountDetails";
import Orders from "./components/UserInfor/Orders";
import OrderDetail from "./components/UserInfor/OrderDetail";
import Mypayment from "./components/UserInfor/Mypayment";
import Mycoupon from "./components/UserInfor/Mycoupon";
import ProductDetail from "./components/Product/ProductDetail";
export default function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="user" element={<UserInfor />}>
            <Route index element={<AccountDetails />} />
            <Route path="address" element={<MyAddress />} />
            <Route path="payment" element={<Mypayment />} />
            <Route path="coupons" element={<Mycoupon />} />
            <Route path="order">
              <Route index element={<Orders />} />
              <Route path=":orderId" element={<OrderDetail />} />
            </Route>
          </Route>
        </Route>
        <Route path="login" element={<Login />} />
        <Route path="register" element={<Register />} />
        <Route path="/" element={<Layout />}>
          {/* các route con */}
          <Route path="product/:id" element={<ProductDetail />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}
