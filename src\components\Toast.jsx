import React, { useEffect } from "react";

export default function Toast({
  message,
  type = "success",
  show,
  onClose,
  duration = 3000,
}) {
  useEffect(() => {
    if (show) {
      const timer = setTimeout(() => {
        onClose && onClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [show, onClose, duration]);

  if (!show) return null;

  let bgColor = "bg-green-400";
  if (type === "error") bgColor = "bg-red-500";
  if (type === "info") bgColor = "bg-blue-500";
  if (type === "warning") bgColor = "bg-yellow-500 text-black";

  return (
    <div
      className={`fixed top-5 right-5 z-50 px-6 py-3 rounded shadow-lg text-white font-semibold transition-all ${bgColor}`}
      style={{ minWidth: 200 }}
      role="alert"
    >
      {message}
      <button
        className="ml-4 text-white font-bold"
        onClick={onClose}
        aria-label="Close"
      >
        ×
      </button>
    </div>
  );
}
