import React, { useState } from "react";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCartPlus } from "@fortawesome/free-solid-svg-icons";
const products = [
  {
    id: 1,
    name: "Product 1",
    image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e",
    price: 199000,
  },
  {
    id: 2,
    name: "Product 2",
    image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e",
    price: 299000,
  },
  {
    id: 3,
    name: "Product 3",
    image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e",
    price: 399000,
  },
  {
    id: 4,
    name: "Product 4",
    image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e",
    price: 499000,
  },
  {
    id: 5,
    name: "Product 5",
    image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e",
    price: 599000,
  },
  {
    id: 6,
    name: "Product 6",
    image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e",
    price: 699000,
  },
  {
    id: 7,
    name: "Product 7",
    image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e",
    price: 799000,
  },
  {
    id: 8,
    name: "Product 8",
    image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e",
    price: 899000,
  },
  // ...thêm sản phẩm nếu muốn
];

export default function ProductLits() {
  return (
    <div>
      <h1 className="text-2xl">Products</h1>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {products.map((product) => (
          <div
            key={product.id}
            className="bg-white rounded-xl shadow-md p-3 flex flex-col items-center hover:scale-105 transition-transform duration-300"
          >
            <img
              src={product.image}
              alt={product.name}
              className="w-48 h-48 object-contain mb-2"
            />
            <h3 className="text-sm font-semibold text-center mb-1">
              {product.name}
            </h3>
            <div className="text-green-600 font-bold mb-2">
              {product.price.toLocaleString()}₫
            </div>
            <button className="bg-green-500 hover:bg-green-600 text-white p-2 rounded-full transition">
              <FontAwesomeIcon icon={faCartPlus} /> Add to cart
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
