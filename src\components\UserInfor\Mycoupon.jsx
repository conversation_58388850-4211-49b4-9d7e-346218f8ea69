import React, { useState } from "react";
import Toast from "../Toast";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrash } from "@fortawesome/free-solid-svg-icons";

const initialCoupons = [
  {
    code: "SALE50",
    desc: "Giảm 50% cho đơn từ 500k",
    expiry: "31/12/2025",
  },
  {
    code: "FREESHIP",
    desc: "Miễn phí vận chuyển toàn quốc",
    expiry: "30/09/2025",
  },
  {
    code: "WELCOME10",
    desc: "Giảm 10% cho khách mới",
    expiry: "31/08/2025",
  },
];

export default function Mycoupon() {
  const [coupons, setCoupons] = useState(initialCoupons);
  const [toast, setToast] = useState({
    show: false,
    message: "",
    type: "success",
  });
  const [confirmDeleteIdx, setConfirmDeleteIdx] = useState(null);

  const handleDelete = (idx) => {
    setConfirmDeleteIdx(idx);
  };

  const confirmDelete = () => {
    if (confirmDeleteIdx !== null) {
      const newCoupons = coupons.filter((_, i) => i !== confirmDeleteIdx);
      setCoupons(newCoupons);
      setToast({
        show: true,
        message: "Xóa coupon thành công!",
        type: "success",
      });
      setConfirmDeleteIdx(null);
    }
  };

  const cancelDelete = () => {
    setConfirmDeleteIdx(null);
  };

  return (
    <div className="col-span-4">
      <Toast
        show={toast.show}
        message={toast.message}
        type={toast.type}
        onClose={() => setToast({ ...toast, show: false })}
      />
      <h1 className="text-2xl md:text-3xl font-semibold m-4">
        Danh sách Coupon
      </h1>
      <div className="space-y-4 m-4">
        {coupons.length === 0 && (
          <div className="text-gray-500">Bạn chưa có coupon nào.</div>
        )}
        {coupons.map((coupon, idx) => (
          <div
            key={idx}
            className="bg-gray-50 rounded-xl p-4 shadow-md flex items-center justify-between gap-4"
          >
            <div className="flex flex-col gap-1 flex-1">
              <span className="font-semibold text-lg text-blue-600">
                {coupon.code}
              </span>
              <span className="text-gray-700">{coupon.desc}</span>
              <span className="text-gray-500">HSD: {coupon.expiry}</span>
            </div>
            <button type="button" onClick={() => handleDelete(idx)}>
              <FontAwesomeIcon
                icon={faTrash}
                className="text-3xl text-red-500 cursor-pointer hover:text-red-700"
              />
            </button>
          </div>
        ))}
      </div>
      {/* Xác nhận xóa */}
      {confirmDeleteIdx !== null && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black opacity-90">
          <div className="bg-white rounded-lg shadow-lg p-6 min-w-[300px]">
            <p className="mb-4 text-lg">
              Bạn có chắc chắn muốn xóa coupon này không?
            </p>
            <div className="flex gap-4 justify-end">
              <button
                onClick={confirmDelete}
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
              >
                Đồng ý
              </button>
              <button
                onClick={cancelDelete}
                className="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"
              >
                Hủy
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
