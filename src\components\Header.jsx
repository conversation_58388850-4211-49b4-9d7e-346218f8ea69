import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faMagnifyingGlass,
  faCartShopping,
  faGift,
  faUser,
} from "@fortawesome/free-solid-svg-icons";
import { useState } from "react";
import Navbar from "./Navbar";
import { Link } from "react-router";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };
  return (
    <header className="bg-[#29c16e] shadow-md">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        {/* Logo */}
        <div className="  flex items-center justify-center">
          <h2 className=" text-white font-semibold md:text-2xl text-sm bg-[#037E4B] rounded-lg px-2 py-2 shadow-lg ">
            GROUP 01
          </h2>
        </div>
        {/* Desktop navigation */}
        <div className="hidden md:block bg-white px-2 rounded-xl shadow-md ">
          <FontAwesomeIcon icon={faMagnifyingGlass} className="text-xl" />
          <input
            type="search"
            placeholder="Search product"
            className=" px-4 py-2 focus:outline-none w-98"
          />
        </div>
        <div className=" items-center gap-2  flex ">
          <div className="bg-[#037E4B] px-2 py-1 rounded-lg border-1 border-[#037E4B] flex items-center">
            <FontAwesomeIcon
              icon={faCartShopping}
              className="text-2xl text-white px-2"
            />
            <span className="text-lg px-2  text-white sm:block hidden">
              Cart
            </span>
          </div>
          <Link
            to={"/login"}
            className="rounded-lg border-1 border-gray-50 px-2 py-1 flex items-center "
          >
            <FontAwesomeIcon
              icon={faUser}
              className="text-2xl text-white px-2"
            />
            <span className="text-lg px-2  text-white sm:block hidden">
              Login
            </span>
          </Link>
          {/* Mobile */}
          <button
            className="md:hidden focus:outline-none rounded-lg border-1 border-gray-50 px-4 py-2"
            aria-label="Toggle menu"
            onClick={toggleMenu}
          >
            {/* Icon 3 gạch (Hamburger) */}
            <div className="w-6 flex flex-col space-y-1  ">
              <span
                className={`h-0.5 w-full bg-white transition text-2xl ${
                  isMenuOpen ? "transform rotate-45 translate-y-1.5" : ""
                } `}
              ></span>
              <span
                className={`h-0.5 w-full bg-white transition ext-2xl ${
                  isMenuOpen ? "opacity-0" : "opacity-100"
                }`}
              ></span>
              <span
                className={`h-0.5 w-full bg-white transition ext-2xl ${
                  isMenuOpen ? "transform -rotate-45 -translate-y-1.5" : ""
                }`}
              ></span>
            </div>
          </button>
        </div>
      </div>
      {/* Mobile Menu - Hiển thị khi click nút menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-[primary] pb-4  ">
          <div className=" bg-white px-2 rounded-xl shadow-md m-4 ">
            <FontAwesomeIcon icon={faMagnifyingGlass} className="text-xl" />
            <input
              type="search"
              placeholder="Search product"
              className=" px-4 py-2 focus:outline-none w-70 "
            />
          </div>
          <ul className="flex flex-col space-y-3 px-4">
            <li>
              <a
                href="#"
                className="block py-2 hover:text-blue-200 transition text-xl text-white"
              >
                Home
              </a>
            </li>
            <li>
              <a
                href="#"
                className="block py-2 hover:text-blue-200 transition text-xl text-white"
              >
                Shop
              </a>
            </li>
            <li>
              <a
                href="#"
                className="block py-2 hover:text-blue-200 transition text-xl text-white"
              >
                Discount
              </a>
            </li>
            <li>
              <a
                href="#"
                className="block py-2 hover:text-blue-200 transition text-xl text-white"
              >
                Blog
              </a>
            </li>
            <li>
              <a
                href="#"
                className="block py-2 hover:text-blue-200 transition text-xl text-white"
              >
                Contact
              </a>
            </li>
            <li>
              <a
                href="#"
                className="block py-2 hover:text-blue-200 transition text-xl text-white"
              >
                Offers
              </a>
            </li>
            <li>
              <a
                href="#"
                className="flex items-center gap-2 text-xl text-white"
              >
                <span className="text-white text-xl">Get your Coupon Code</span>
                <FontAwesomeIcon
                  icon={faGift}
                  className="text-2xl text-[#037E4B]"
                />
              </a>
            </li>
          </ul>
        </div>
      )}
    </header>
  );
}
