import React, { useState } from "react";

export default function Coment({ onAdd }) {
  const [user, setUser] = useState("");
  const [content, setContent] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!user.trim() || !content.trim()) {
      setError("Vui lòng nhập tên và nội dung bình luận.");
      return;
    }
    onAdd({ user, content });
    setUser("");
    setContent("");
    setError("");
  };

  return (
    <form onSubmit={handleSubmit} className="mb-6 flex flex-col gap-2">
      <input
        type="text"
        placeholder="Tên của bạn"
        className="border rounded px-3 py-2"
        value={user}
        onChange={(e) => setUser(e.target.value)}
      />
      <textarea
        placeholder="Nội dung bình luận..."
        className="border rounded px-3 py-2"
        value={content}
        onChange={(e) => setContent(e.target.value)}
        rows={3}
      />
      {error && <div className="text-red-500 text-sm">{error}</div>}
      <button
        type="submit"
        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded w-max"
      >
        Gửi bình luận
      </button>
    </form>
  );
}
