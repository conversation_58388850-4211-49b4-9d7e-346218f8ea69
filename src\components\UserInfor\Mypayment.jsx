import React, { useState } from "react";
import Toast from "../Toast";
import { faEdit, faSave } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes, faTrash } from "@fortawesome/free-solid-svg-icons";
import { faCreditCard } from "@fortawesome/free-regular-svg-icons";
import { faPlusCircle } from "@fortawesome/free-solid-svg-icons";
const initialCards = [
  {
    cardNumber: "**** **** **** 1234",
    cardHolder: "PHI HOANG ANH",
    expiry: "12/27",
  },
  {
    cardNumber: "**** **** **** 5678",
    cardHolder: "PHI HOANG ANH",
    expiry: "09/26",
  },
];

export default function Mypayment() {
  const [cards, setCards] = useState(initialCards);
  const [editingIdx, setEditingIdx] = useState(null);
  const [form, setForm] = useState({
    cardNumber: "",
    cardHolder: "",
    expiry: "",
  });
  const [toast, setToast] = useState({
    show: false,
    message: "",
    type: "success",
  });
  const [confirmDeleteIdx, setConfirmDeleteIdx] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [addForm, setAddForm] = useState({
    cardNumber: "",
    cardHolder: "",
    expiry: "",
  });
  const handleAddChange = (e) => {
    setAddForm({ ...addForm, [e.target.name]: e.target.value });
  };

  const handleAddCard = (e) => {
    e.preventDefault();
    setCards([...cards, { ...addForm }]);
    setAddForm({ cardNumber: "", cardHolder: "", expiry: "" });
    setShowAddForm(false);
    setToast({ show: true, message: "Thêm thẻ thành công!", type: "success" });
  };

  const handleEdit = (idx) => {
    setEditingIdx(idx);
    setForm(cards[idx]);
  };

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSave = (idx) => {
    const newCards = cards.map((item, i) => (i === idx ? { ...form } : item));
    setCards(newCards);
    setEditingIdx(null);
    setToast({
      show: true,
      message: "Cập nhật thẻ thành công!",
      type: "success",
    });
  };

  const handleCancel = () => {
    setEditingIdx(null);
  };

  const handleDelete = (idx) => {
    setConfirmDeleteIdx(idx);
  };

  const confirmDelete = () => {
    if (confirmDeleteIdx !== null) {
      const newCards = cards.filter((_, i) => i !== confirmDeleteIdx);
      setCards(newCards);
      setToast({ show: true, message: "Xóa thẻ thành công!", type: "success" });
      setConfirmDeleteIdx(null);
    }
  };

  const cancelDelete = () => {
    setConfirmDeleteIdx(null);
  };

  return (
    <div className="col-span-4">
      <Toast
        show={toast.show}
        message={toast.message}
        type={toast.type}
        onClose={() => setToast({ ...toast, show: false })}
      />
      <h1 className="text-2xl md:text-3xl font-semibold m-4">
        Danh sách thẻ thanh toán
      </h1>
      <div className="flex justify-end m-4">
        <button
          className="  text-green-500 px-4 py-2 rounded font-semibold flex flex-row items-center gap-2"
          onClick={() => setShowAddForm(true)}
        >
          <FontAwesomeIcon
            icon={faPlusCircle}
            className="text-4xl text-green-500 cursor-pointer hover:text-green-600"
          />
          <span className="text-xl">Thêm thẻ mới</span>
        </button>
      </div>
      {showAddForm && (
        <form
          onSubmit={handleAddCard}
          className="bg-gray-50 rounded-xl p-4 shadow-md flex flex-col gap-2 m-4 max-w-md"
        >
          <h2 className="font-semibold text-lg mb-2">Thêm thẻ mới</h2>
          <input
            type="text"
            name="cardNumber"
            value={addForm.cardNumber}
            onChange={handleAddChange}
            className="border rounded px-2 py-1 mb-1"
            required
            placeholder="Số thẻ"
          />
          <input
            type="text"
            name="cardHolder"
            value={addForm.cardHolder}
            onChange={handleAddChange}
            className="border rounded px-2 py-1 mb-1"
            required
            placeholder="Tên chủ thẻ"
          />
          <input
            type="text"
            name="expiry"
            value={addForm.expiry}
            onChange={handleAddChange}
            className="border rounded px-2 py-1 mb-2"
            required
            placeholder="MM/YY"
          />
          <div className="flex gap-2">
            <button
              type="submit"
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
            >
              Lưu
            </button>
            <button
              type="button"
              onClick={() => setShowAddForm(false)}
              className="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"
            >
              Hủy
            </button>
          </div>
        </form>
      )}
      <div className="space-y-4 m-4">
        {cards.map((card, idx) => (
          <div
            key={idx}
            className="bg-gray-50 rounded-xl p-4 shadow-md flex items-center justify-between gap-4"
          >
            <FontAwesomeIcon
              icon={faCreditCard}
              className="text-3xl text-green-500"
            />
            {editingIdx === idx ? (
              <div className="flex flex-col gap-2 flex-1">
                <input
                  type="text"
                  name="cardNumber"
                  value={form.cardNumber}
                  onChange={handleChange}
                  className="border rounded px-2 py-1 mb-1"
                  required
                  placeholder="Số thẻ"
                />
                <input
                  type="text"
                  name="cardHolder"
                  value={form.cardHolder}
                  onChange={handleChange}
                  className="border rounded px-2 py-1 mb-1"
                  required
                  placeholder="Tên chủ thẻ"
                />
                <input
                  type="text"
                  name="expiry"
                  value={form.expiry}
                  onChange={handleChange}
                  className="border rounded px-2 py-1"
                  required
                  placeholder="MM/YY"
                />
              </div>
            ) : (
              <div className="flex flex-col gap-1 flex-1">
                <span className="font-semibold text-lg">{card.cardNumber}</span>
                <span className="text-gray-700">{card.cardHolder}</span>
                <span className="text-gray-500">Hết hạn: {card.expiry}</span>
              </div>
            )}
            <div className="flex gap-2 ml-4">
              {editingIdx === idx ? (
                <>
                  <button
                    type="button"
                    onClick={() => handleSave(idx)}
                    className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded flex items-center gap-2"
                  >
                    <FontAwesomeIcon icon={faSave} /> Lưu
                  </button>
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded flex items-center gap-2"
                  >
                    <FontAwesomeIcon icon={faTimes} /> Hủy
                  </button>
                </>
              ) : (
                <>
                  <button type="button" onClick={() => handleEdit(idx)}>
                    <FontAwesomeIcon
                      icon={faEdit}
                      className="text-3xl text-green-500 cursor-pointer hover:text-green-600"
                    />
                  </button>
                  <button type="button" onClick={() => handleDelete(idx)}>
                    <FontAwesomeIcon
                      icon={faTrash}
                      className="text-3xl text-red-500 cursor-pointer hover:text-red-700"
                    />
                  </button>
                </>
              )}
            </div>
          </div>
        ))}
      </div>
      {/* Xác nhận xóa */}
      {confirmDeleteIdx !== null && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black opacity-90">
          <div className="bg-white rounded-lg shadow-lg p-6 min-w-[300px]">
            <p className="mb-4 text-lg">
              Bạn có chắc chắn muốn xóa thẻ này không?
            </p>
            <div className="flex gap-4 justify-end">
              <button
                onClick={confirmDelete}
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
              >
                Đồng ý
              </button>
              <button
                onClick={cancelDelete}
                className="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded"
              >
                Hủy
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
