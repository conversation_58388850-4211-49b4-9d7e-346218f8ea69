import { useState } from "react";
import Carousel from "../components/Carousel";
import Category from "../components/Category";

import ProductLits from "../components/Product/ProductLits.jsx";
import Pagination from "../components/Pagination";
function Home() {
  const slides = [
    { url: "https://images.unsplash.com/photo-1542281286-9e0a16bb7366" },
    { url: "https://images.unsplash.com/photo-1506744038136-46273834b3fb" },
    { url: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e" },
  ];
  const categories = [
    {
      id: 1,
      name: "Electronics",
      image: "https://images.unsplash.com/photo-1506744038136-46273834b3fb",
    },
    {
      id: 2,
      name: "Fashion",
      image: "https://images.unsplash.com/photo-1506744038136-46273834b3fb",
    },
    {
      id: 3,
      name: "Home & Garden",
      image: "https://images.unsplash.com/photo-1506744038136-46273834b3fb",
    },
    {
      id: 4,
      name: "<PERSON>",
      image: "https://images.unsplash.com/photo-1506744038136-46273834b3fb",
    },
    {
      id: 5,
      name: "Sports",
      image: "https://images.unsplash.com/photo-1506744038136-46273834b3fb",
    },
    {
      id: 6,
      name: "Sports",
      image: "https://images.unsplash.com/photo-1506744038136-46273834b3fb",
    },
  ];
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = 25; // This would typically come from your API or data source

  const handlePageChange = (page) => {
    setCurrentPage(page);
    // Here you would typically fetch data for the new page
    console.log("Page changed to:", page);
  };
  return (
    <div className="mx-auto max-w-7xl px-4 py-8  justify-center">
      <Carousel slides={slides} />

      <Category categories={categories} />
      <ProductLits />
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
      />
    </div>
  );
}

export default Home;
