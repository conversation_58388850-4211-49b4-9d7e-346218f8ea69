import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faGift, faAngleDown } from "@fortawesome/free-solid-svg-icons";
import { useState } from "react";

export default function Navbar() {
  return (
    <nav className=" hidden md:flex items-center justify-between p-4 bg-green-100">
      <div className="relative group inline-block">
        <div className="flex items-center gap-2 cursor-pointer">
          <h3 className="text-xl ">Shop By Department</h3>   
          <svg
            className="w-4 h-4 mt-1 transition-transform duration-200 group-hover:rotate-180"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strork-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 9l-7 7-7-7"
            ></path>
          </svg>
        </div>

        <ul className="absolute hidden bg-white text-gray-700 shadow-lg rounded-md  py-1 w-48 z-10 group-hover:block text-xl">
          <li>
            <a href="#" className="block px-4 py-2 hover:bg-gray-100">
              Danh mục 1
            </a>
          </li>

          <li>
            <a href="#" className="block px-4 py-2 hover:bg-gray-100">
              Danh mục 1
            </a>
          </li>

          <li>
            <a href="#" className="block px-4 py-2 hover:bg-gray-100">
              Danh mục 1
            </a>
          </li>

          <li>
            <a href="#" className="block px-4 py-2 hover:bg-gray-100">
              Danh mục 1
            </a>
          </li>
        </ul>
      </div>

      <div>
        <ul className="flex items-center gap-4 text-xl">
          <li>
            <a href="">Home</a>
          </li>

          <li>
            <a href="">Shop</a>
          </li>

          <li>
            <a href="">Discount</a>
          </li>

          <li>
            <a href="">Service</a>
          </li>
          <li>
            <a href="">Blog</a>
          </li>

          <li>
            <a href="">Contact</a>
          </li>

          <li>
            <a href="">Offers</a>
          </li>
        </ul>
      </div>

      <div className="flex items-center gap-2">
        <FontAwesomeIcon icon={faGift} className="text-2xl text-[#037E4B]" />
        <span className="text-black text-xl hover:text-gray-600 cursor-pointer">
          Get your Coupon Code
        </span>
      </div>
    </nav>
  );
}
