import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowLeft } from "@fortawesome/free-solid-svg-icons";
import { Link } from "react-router";

export default function ButtonArrow() {
  return (
    <Link
      to={"/"}
      className="flex items-center gap-2 px-4 bg-white py-3.5 hover:bg-green-700 text-[primary] hover:text-white rounded-full transition absolute top-4 left-4"
    >
      <FontAwesomeIcon icon={faArrowLeft} className="text-2xl" />
    </Link>
  );
}
