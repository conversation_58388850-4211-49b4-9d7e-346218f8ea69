import React, { useState } from "react";
import Toast from "../Toast";
import { faEdit, faSave } from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";

export default function AccountDetails() {
  const [details, setDetails] = useState({
    fullName: "Phí Hoàng Anh",
    mobile: "*********",
    email: "Bắc <PERSON> Liêm,Hà Nội",
  });
  const [form, setForm] = useState(details);
  const [editingField, setEditingField] = useState(null);
  const [toast, setToast] = useState({
    show: false,
    message: "",
    type: "success",
  });

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleEdit = (field) => {
    setForm(details);
    setEditingField(field);
  };

  const handleCancel = () => {
    setForm(details);
    setEditingField(null);
  };

  const handleSave = (field) => {
    setDetails({ ...details, [field]: form[field] });
    setEditingField(null);
    setToast({ show: true, message: "Cập nhật thành công!", type: "success" });
  };

  return (
    <div className="col-span-4">
      <Toast
        show={toast.show}
        message={toast.message}
        type={toast.type}
        onClose={() => setToast({ ...toast, show: false })}
      />
      <h1 className="text-2xl  md:text-3xl font-semibold m-4">
        Account Details
      </h1>
      <form onSubmit={(e) => e.preventDefault()}>
        {/* Full Name */}
        <div className="bg-gray-50 rounded-xl m-4 p-4 shadow-md flex items-center justify-between">
          <div className="flex flex-col gap-2">
            <p className="font-semibold text-xl">Full Name</p>
            {editingField === "fullName" ? (
              <input
                type="text"
                name="fullName"
                value={form.fullName}
                onChange={handleChange}
                className="border rounded px-2 py-1"
                required
              />
            ) : (
              <p className="text-lg">{details.fullName}</p>
            )}
          </div>
          {editingField === "fullName" ? (
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => handleSave("fullName")}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded flex items-center gap-2"
              >
                <FontAwesomeIcon icon={faSave} />
                Save
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded flex items-center gap-2"
              >
                <FontAwesomeIcon icon={faTimes} />
                Cancel
              </button>
            </div>
          ) : (
            <button type="button" onClick={() => handleEdit("fullName")}>
              <FontAwesomeIcon
                icon={faEdit}
                className="text-3xl text-green-500 cursor-pointer hover:text-green-600"
              />
            </button>
          )}
        </div>
        {/* Mobile Number */}
        <div className="bg-gray-50 rounded-xl m-4 p-4 shadow-md flex items-center justify-between">
          <div className="flex flex-col gap-2">
            <p className="font-semibold text-xl">Mobile Number</p>
            {editingField === "mobile" ? (
              <input
                type="text"
                name="mobile"
                value={form.mobile}
                onChange={handleChange}
                className="border rounded px-2 py-1"
                required
              />
            ) : (
              <p className="text-lg">{details.mobile}</p>
            )}
          </div>
          {editingField === "mobile" ? (
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => handleSave("mobile")}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded flex items-center gap-2"
              >
                <FontAwesomeIcon icon={faSave} />
                Save
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded flex items-center gap-2"
              >
                <FontAwesomeIcon icon={faTimes} />
                Cancel
              </button>
            </div>
          ) : (
            <button type="button" onClick={() => handleEdit("mobile")}>
              <FontAwesomeIcon
                icon={faEdit}
                className="text-3xl text-green-500 cursor-pointer hover:text-green-600"
              />
            </button>
          )}
        </div>
        {/* Email Address */}
        <div className="bg-gray-50 rounded-xl m-4 p-4 shadow-md flex items-center justify-between">
          <div className="flex flex-col gap-2">
            <p className="font-semibold text-xl">Email Address</p>
            {editingField === "email" ? (
              <input
                type="text"
                name="email"
                value={form.email}
                onChange={handleChange}
                className="border rounded px-2 py-1"
                required
              />
            ) : (
              <p className="text-lg">{details.email}</p>
            )}
          </div>
          {editingField === "email" ? (
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => handleSave("email")}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded flex items-center gap-2"
              >
                <FontAwesomeIcon icon={faSave} />
                Save
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="bg-gray-300 hover:bg-gray-400 text-black px-4 py-2 rounded flex items-center gap-2"
              >
                <FontAwesomeIcon icon={faTimes} />
                Cancel
              </button>
            </div>
          ) : (
            <button type="button" onClick={() => handleEdit("email")}>
              <FontAwesomeIcon
                icon={faEdit}
                className="text-3xl text-green-500 cursor-pointer hover:text-green-600"
              />
            </button>
          )}
        </div>
      </form>
    </div>
  );
}
