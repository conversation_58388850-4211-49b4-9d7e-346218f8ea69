
import React from "react";

export default function ListComent({ comments }) {
  if (!comments || comments.length === 0)
    return <div className="text-gray-500">Ch<PERSON><PERSON> c<PERSON> bình luận nào.</div>;
  return (
    <div className="space-y-4">
      {comments.map((c, idx) => (
        <div key={idx} className="border-b pb-3">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-semibold text-blue-600">{c.user}</span>
            <span className="text-xs text-gray-400">{c.date}</span>
          </div>
          <div className="text-gray-800">{c.content}</div>
        </div>
      ))}
    </div>
  );
}
