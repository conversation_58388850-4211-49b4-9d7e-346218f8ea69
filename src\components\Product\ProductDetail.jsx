
import React, { useState } from "react";
import Coment from "./Coment";
import ListComent from "./ListComent";

const sampleProduct = {
  id: 1,
  name: "Product 1",
  image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e",
  price: 199000,
  description: "Sản phẩm chất lư<PERSON>ng cao, phù hợp mọi nhu cầu.",
};

export default function ProductDetail() {
  const [comments, setComments] = useState([
    {
      user: "Nguyễn Văn A",
      content: "Sản phẩm rất tốt!",
      date: "2025-07-20",
    },
    {
      user: "Trần Thị B",
      content: "<PERSON><PERSON><PERSON> gói cẩn thận, giao hàng nhanh.",
      date: "2025-07-21",
    },
  ]);

  const handleAddComment = (comment) => {
    setComments([
      { ...comment, date: new Date().toLocaleDateString("vi-VN") },
      ...comments,
    ]);
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="flex flex-col md:flex-row gap-8 bg-white rounded-xl shadow-md p-6 mb-6">
        <img
          src={sampleProduct.image}
          alt={sampleProduct.name}
          className="w-64 h-64 object-contain rounded"
        />
        <div className="flex-1 flex flex-col gap-2">
          <h1 className="text-2xl font-bold mb-2">{sampleProduct.name}</h1>
          <div className="text-green-600 font-bold text-xl mb-2">
            {sampleProduct.price.toLocaleString()}₫
          </div>
          <div className="mb-2 text-gray-700">{sampleProduct.description}</div>
          <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded w-max">
            Thêm vào giỏ hàng
          </button>
        </div>
      </div>
      <div className="bg-white rounded-xl shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Bình luận sản phẩm</h2>
        <Coment onAdd={handleAddComment} />
        <ListComent comments={comments} />
      </div>
    </div>
  );
}
