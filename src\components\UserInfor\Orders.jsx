import React from "react";
import { useNavigate } from "react-router";
export default function Orders() {
  const orders = [
    {
      id: "OD001",
      date: "2025-07-20",
      status: "Đang xử lý",
      total: 1200000,
    },
    {
      id: "OD002",
      date: "2025-07-15",
      status: "Đã giao",
      total: 850000,
    },
    {
      id: "OD003",
      date: "2025-07-10",
      status: "Đã hủy",
      total: 500000,
    },
  ];
  const navigate = useNavigate();
  return (
    <div className="col-span-4">
      <h1 className="text-2xl md:text-3xl font-semibold m-4">
        <PERSON>h sách đơn hàng
      </h1>
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white rounded-xl shadow-md">
          <thead>
            <tr className="bg-gray-100 text-left">
              <th className="py-3 px-4"><PERSON><PERSON> đơn</th>
              <th className="py-3 px-4"><PERSON><PERSON><PERSON> đặt</th>
              <th className="py-3 px-4">Tr<PERSON>ng thái</th>
              <th className="py-3 px-4">Tổng tiền</th>
              <th className="py-3 px-4">Hành động</th>
            </tr>
          </thead>
          <tbody>
            {orders.map((order) => (
              <tr key={order.id} className="border-b hover:bg-gray-50">
                <td className="py-2 px-4 font-semibold">{order.id}</td>
                <td className="py-2 px-4">{order.date}</td>
                <td className="py-2 px-4">
                  <span
                    className={
                      order.status === "Đã giao"
                        ? "bg-green-500 font-medium text-white px-2 py-1 rounded-2xl  "
                        : order.status === "Đã hủy"
                        ? "bg-red-500 font-medium  text-white  px-2 py-1 rounded-2xl "
                        : "bg-yellow-600 font-medium  text-white px-2 py-1 rounded-2xl  "
                    }
                  >
                    {order.status}
                  </span>
                </td>
                <td className="py-2 px-4">{order.total.toLocaleString()}₫</td>
                <td className="py-2 px-4">
                  <button
                    className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded"
                    onClick={() => navigate(`/user/order/${order.id}`)}
                  >
                    Xem chi tiết
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
  // ...existing code...
}
